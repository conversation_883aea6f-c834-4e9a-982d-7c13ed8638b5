=========================================
POSTGRESQL PROCEDURE TESTING
Started: Fri 12 Sep 2025 04:28:10 PM PDT
=========================================
Running tests for procedure: getaudit
=========================================
Testing procedure: getaudit
=========================================
  Running test: get_audit (params: 2024-01-01:2024-12-31)
    SQL Server: EXEC [dbo].[GetAudit] @fromDate = '2024-01-01', @toDate = '2024-12-31';
    PostgreSQL: SELECT * FROM dbo.getaudit('2024-01-01', '2024-12-31')
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: PostgreSQL failed: ERROR:  relation "dbo.aspnetusers" does not exist
LINE 12:     JOIN dbo.aspnetusers u ON u.userid = l.userid
                  ^
QUERY:  SELECT 
        l.id,
        l.userid,
        u.email as username,
        l.ipaddress,
        l.eventtype,
        l.eventdatetime,
        l.tablename,
        l.patientrecordid,
        l.changes
    FROM dbo.audits l
    JOIN dbo.aspnetusers u ON u.userid = l.userid
    LEFT JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN l.changes IS NOT NULL AND jsonb_typeof(l.changes::jsonb) = 'object' 
                    THEN (l.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array ON (p_content IS NOT NULL AND p_content != '')
    WHERE l.eventdatetime > p_from_date 
      AND l.eventdatetime < p_to_date
      -- Optional IP address filter
      AND (p_ip_address IS NULL OR TRIM(p_ip_address) = '' OR l.ipaddress ILIKE '%' || TRIM(p_ip_address) || '%')
      -- Optional user ID filter
      AND (p_user_id IS NULL OR p_user_id <= 0 OR l.userid = p_user_id)
      -- Optional patient record ID filter
      AND (p_patient_record_id IS NULL OR p_patient_record_id <= 0 OR l.patientrecordid = p_patient_record_id)
      -- Optional content search in JSON changes
      AND (p_content IS NULL OR TRIM(p_content) = '' OR (
          l.changes IS NOT NULL
          AND l.changes != ''
          AND jsonb_typeof(l.changes::jsonb) = 'object'
          AND (l.changes::jsonb -> 'changes') IS NOT NULL
          AND jsonb_typeof(l.changes::jsonb -> 'changes') = 'array'
          AND (
              (changes_array.change_item ->> 'OV') ILIKE '%' || TRIM(p_content) || '%'
              OR (changes_array.change_item ->> 'NV') ILIKE '%' || TRIM(p_content) || '%'
          )
      ))
    ORDER BY l.eventdatetime
CONTEXT:  PL/pgSQL function dbo.getaudit(timestamp without time zone,timestamp without time zone,character varying,integer,integer,character varying) line 3 at RETURN QUERY
  Running test: no_get_audit (params: 2025-01-01:2025-01-31)
    SQL Server: EXEC [dbo].[GetAudit] @fromDate = '2025-01-01', @toDate = '2025-01-31';
    PostgreSQL: SELECT * FROM dbo.getaudit('2025-01-01', '2025-01-31')
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: PostgreSQL failed: ERROR:  relation "dbo.aspnetusers" does not exist
LINE 12:     JOIN dbo.aspnetusers u ON u.userid = l.userid
                  ^
QUERY:  SELECT 
        l.id,
        l.userid,
        u.email as username,
        l.ipaddress,
        l.eventtype,
        l.eventdatetime,
        l.tablename,
        l.patientrecordid,
        l.changes
    FROM dbo.audits l
    JOIN dbo.aspnetusers u ON u.userid = l.userid
    LEFT JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN l.changes IS NOT NULL AND jsonb_typeof(l.changes::jsonb) = 'object' 
                    THEN (l.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array ON (p_content IS NOT NULL AND p_content != '')
    WHERE l.eventdatetime > p_from_date 
      AND l.eventdatetime < p_to_date
      -- Optional IP address filter
      AND (p_ip_address IS NULL OR TRIM(p_ip_address) = '' OR l.ipaddress ILIKE '%' || TRIM(p_ip_address) || '%')
      -- Optional user ID filter
      AND (p_user_id IS NULL OR p_user_id <= 0 OR l.userid = p_user_id)
      -- Optional patient record ID filter
      AND (p_patient_record_id IS NULL OR p_patient_record_id <= 0 OR l.patientrecordid = p_patient_record_id)
      -- Optional content search in JSON changes
      AND (p_content IS NULL OR TRIM(p_content) = '' OR (
          l.changes IS NOT NULL
          AND l.changes != ''
          AND jsonb_typeof(l.changes::jsonb) = 'object'
          AND (l.changes::jsonb -> 'changes') IS NOT NULL
          AND jsonb_typeof(l.changes::jsonb -> 'changes') = 'array'
          AND (
              (changes_array.change_item ->> 'OV') ILIKE '%' || TRIM(p_content) || '%'
              OR (changes_array.change_item ->> 'NV') ILIKE '%' || TRIM(p_content) || '%'
          )
      ))
    ORDER BY l.eventdatetime
CONTEXT:  PL/pgSQL function dbo.getaudit(timestamp without time zone,timestamp without time zone,character varying,integer,integer,character varying) line 3 at RETURN QUERY

  Procedure getaudit summary: 0 passed, 2 failed (out of 2 tests)

